'use client';
import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

export default function Services() {
  // Responsive Font Size wie in Work/About
  const getResponsiveFontSize = (base: number) => `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  const getResponsiveSpacing = (base: number) => `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;

  const cardsRef = useRef<Array<HTMLDivElement | null>>([]);

  // Keine Animation mehr, Karten liegen nur als Fächer

  useEffect(() => {
    if (!cardsRef.current) return;
    const cards = cardsRef.current;
    // ScrollTrigger Pin-Effekt - Services Section wird KOMPLETT gepinnt bis alle Cards fertig sind
    const cardArea = document.getElementById('card-area');
    if (cardArea && cards[0] && cards[1] && cards[2]) {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: cardArea,
          start: 'top top', // Startet wenn Services Section oben ist
          end: '+=5000', // SEHR LANGE Pin-Dauer - Contact Section kommt erst danach!
          pin: true,
          scrub: 1,
          anticipatePin: 1,
        }
      });

      // 0-0.15: Warten vor erster Animation
      tl.to({}, { duration: 0.15 });

      // 0.15-0.25: Card 1 flippt
      tl.to(cards[0].querySelector('.card-inner'), {
        rotateY: 180,
        rotateZ: 0,
        duration: 0.1,
        ease: 'power2.inOut'
      }, 0.15);

      // 0.25-0.4: Lange Pause nach Card 1
      tl.to({}, { duration: 0.15 });

      // 0.4-0.5: Card 2 flippt
      tl.to(cards[1].querySelector('.card-inner'), {
        rotateY: 180,
        rotateZ: 0,
        duration: 0.1,
        ease: 'power2.inOut'
      }, 0.4);

      // 0.5-0.65: Lange Pause nach Card 2
      tl.to({}, { duration: 0.15 });

      // 0.65-0.75: Card 3 flippt
      tl.to(cards[2].querySelector('.card-inner'), {
        rotateY: 180,
        rotateZ: 0,
        duration: 0.1,
        ease: 'power2.inOut'
      }, 0.65);

      // 0.75-1: SEHR LANGE finale Pause - Contact Section kommt erst NACH dieser Animation!
      tl.to({}, { duration: 0.25 });
    }
    // Subtile Scale-Schweb-Animation bleibt aktiv, wenn nicht gepinnt
    const tweens: gsap.core.Tween[] = [];
    [0, 1, 2].forEach((i) => {
      const card = cards[i];
      if (!card) return;
      tweens.push(
        gsap.to(card, {
          scale: 1.02,
          duration: 2.6 + i * 0.4,
          yoyo: true,
          repeat: -1,
          ease: 'sine.inOut',
          delay: i * 0.5,
        })
      );
    });
    return () => {
      tweens.forEach(t => t.kill());
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      className="bg-black text-white w-full flex items-center justify-center rounded-t-[120px] lg:rounded-t-[120px] md:rounded-t-[60px] sm:rounded-t-[40px]"
      style={{
        height: '220vh',
        zIndex: 30,
        position: 'relative',
        transition: 'border-radius 0.4s cubic-bezier(0.4,0,0.2,1)',
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
      }}
    >
      {/* Top left label - SERVICES */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: ORANGE,
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (SERVICES)
      </div>
      {/* Right label - 04 in orange, vertikal mittig wie in Work.tsx */}
      <div style={{
        position: 'absolute',
        top: 'clamp(420px, 50vh, 580px)',
        right: 'clamp(20px, 3vw, 40px)',
        color: ORANGE,
        fontSize: getResponsiveFontSize(42),
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (04)
      </div>
      <div style={{
        maxWidth: 900,
        margin: '0 auto',
        padding: 'clamp(20px, 3vw, 40px)',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        marginTop: 10,
        position: 'relative',
      }}>
        <div style={{ padding: 0 }}>
          <div style={{
            color: '#fff',
            fontSize: getResponsiveFontSize(44),
            fontWeight: 700,
            margin: `${getResponsiveSpacing(120)} 0 ${getResponsiveSpacing(48)} 0`,
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
          }}>
            From digital strategy to pixel-perfect implementation, I help brands and businesses turn ideas into real, usable products.<br />
            <span style={{
              fontWeight: 400,
              fontSize: getResponsiveFontSize(24),
              display: 'block',
              marginTop: getResponsiveSpacing(24),
              lineHeight: 1.2,
            }}>
              My focus: clarity, usability, and a touch of boldness.
            </span>
          </div>
        </div>
        {/* Kartenbereich */}
        <div id="card-area" className="flex flex-row gap-0 justify-center items-end w-full" style={{ marginTop: getResponsiveSpacing(320), position: 'relative', height: 550 }}>
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              ref={el => { cardsRef.current[i] = el; }}
              className="card flex-shrink-0"
              style={{
                width: 340,
                height: 480,
                borderRadius: 36,
                position: 'absolute',
                left: `calc(50% - 170px + ${i === 0 ? -36 : i === 2 ? 36 : 0}px)`,
                top: i === 0 ? 24 : i === 1 ? 0 : 48,
                zIndex: 10 - i,
                boxShadow: '0 8px 32px rgba(0,0,0,0.10)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'transparent',
                border: 'none',
                fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                transition: 'none',
                willChange: 'transform',
                perspective: '1200px',
              }}
            >
              <div
                className="card-inner"
                style={{
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                  transformStyle: 'preserve-3d',
                  transition: 'transform 0.7s cubic-bezier(0.4,0,0.2,1)',
                  borderRadius: 36,
                  willChange: 'transform',
                  transform: `rotateY(0deg) rotateZ(${i === 0 ? -9 : i === 2 ? 9 : 0}deg) translateY(${i === 0 ? 10 : i === 1 ? -12 : 8}px)`,
                }}
              >
                {/* Vorderseite */}
                <div
                  className="card-front"
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: i === 0 ? '#ece3fa' : i === 1 ? '#ffe0f6' : '#ffe3b3',
                    borderRadius: 36,
                    display: 'flex',
                    alignItems: 'flex-start',
                    justifyContent: 'flex-start',
                    fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                  }}
                >
                  <span style={{
                    position: 'absolute',
                    top: 24,
                    left: 28,
                    color: '#111',
                    fontSize: 18,
                    fontWeight: 600,
                    letterSpacing: 1.2,
                    opacity: 0.85,
                  }}>
                    {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
                  </span>
                </div>
                {/* Rückseite */}
                <div
                  className="card-back"
                  style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                    backfaceVisibility: 'hidden',
                    background: i === 0 ? '#ece3fa' : i === 1 ? '#ffe0f6' : '#ffe3b3',
                    borderRadius: 36,
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    padding: '48px 32px',
                    zIndex: 2,
                    fontSize: 18,
                    fontWeight: 500,
                    gap: 12,
                    transform: 'rotateY(180deg)',
                    color: '#222',
                  }}
                >
                  {i === 0 && [
                    'Custom Software Solutions',
                    'Code Refactoring',
                    'Product Roadmapping & Planning',
                    'Market & Competitor Analysis',
                  ].map((text, j) => (
                    <div key={`card${i}-item${j}`}>{text}</div>
                  ))}
                  {i === 1 && [
                    'Website Design & Development',
                    'App Design & Development',
                    'UI/UX Design & Redesign',
                    'Logo & Visual Identity Design',
                  ].map((text, j) => (
                    <div key={`card${i}-item${j}`}>{text}</div>
                  ))}
                  {i === 2 && [
                    'Website Design & Development',
                    'App Design & Development',
                    'Custom Software Solutions',
                    'Code Refactoring',
                  ].map((text, j) => (
                    <div key={`card${i}-item${j}`}>{text}</div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

