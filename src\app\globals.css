@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

html {
  overflow-x: hidden;
}

*, *::before, *::after {
  box-sizing: border-box;
}

@font-face {
  font-family: 'Satoshi';
  src: url('/fonts/Satoshi-Variable.ttf') format('truetype');
  font-weight: 100 900;
  font-style: normal;
  font-display: swap;
}

/* Custom Scrollbar Styles */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: #e8561c;
  border-radius: 10px;
  border: 2px solid #f0f0f0;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #d14a18;
}

::-webkit-scrollbar-thumb:active {
  background: #b83d15;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #e8561c #f0f0f0;
}
