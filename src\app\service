'use client';
import { useRef, useEffect } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
gsap.registerPlugin(ScrollTrigger);

const ORANGE = "#e8561c";

export default function Services() {
  // Responsive Font Size wie in Work/About
  const getResponsiveFontSize = (base: number) => `clamp(${base * 0.5}px, ${base * 0.08}vw, ${base}px)`;
  const getResponsiveSpacing = (base: number) => `clamp(${base * 0.5}px, ${base * 0.1}vw, ${base}px)`;

  const cardsRef = useRef<Array<HTMLDivElement | null>>([]);

  useEffect(() => {
    if (!cardsRef.current) return;
    cardsRef.current.forEach((card, i) => {
      if (!card) return;
      gsap.set(card, { rotateY: -90, opacity: 0 });
      ScrollTrigger.create({
        trigger: card,
        start: 'top 80%',
        onEnter: () => {
          gsap.to(card, {
            rotateY: 0,
            opacity: 1,
            duration: 0.8,
            delay: i * 0.18, // gestaffelt
            ease: 'back.out(1.7)',
          });
        },
        once: true,
      });
    });
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <section
      className="bg-black text-white w-full flex items-center justify-center rounded-t-[120px] lg:rounded-t-[120px] md:rounded-t-[60px] sm:rounded-t-[40px]"
      style={{
        height: '220vh',
        zIndex: 30,
        position: 'relative',
        transition: 'border-radius 0.4s cubic-bezier(0.4,0,0.2,1)',
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
      }}
    >
      {/* Top left label */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: ORANGE,
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (SERVICES)
      </div>
      {/* Bottom right label (jetzt auf Höhe des Textes, nicht ganz unten) */}
      <div style={{
        position: 'absolute',
        top: 120,
        right: 40,
        color: ORANGE,
        fontSize: getResponsiveFontSize(42),
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (04)
      </div>
      <div style={{
        maxWidth: 900,
        margin: '0 auto',
        padding: 'clamp(20px, 3vw, 40px)',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        marginTop: 80,
        position: 'relative',
      }}>
        <div style={{ padding: 0 }}>
          <div style={{
            color: '#fff',
            fontSize: getResponsiveFontSize(44),
            fontWeight: 700,
            margin: `${getResponsiveSpacing(120)} 0 ${getResponsiveSpacing(48)} 0`,
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
          }}>
            From digital strategy to pixel-perfect implementation, I help brands and businesses turn ideas into real, usable products.<br />
            <span style={{
              fontWeight: 400,
              fontSize: getResponsiveFontSize(24),
              display: 'block',
              marginTop: getResponsiveSpacing(24),
              lineHeight: 1.2,
            }}>
              My focus: clarity, usability, and a touch of boldness.
            </span>
          </div>
        </div>
        {/* Kartenbereich */}
        <div className="flex flex-row gap-8 justify-center items-end w-full" style={{ marginTop: getResponsiveSpacing(320) }}>
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              ref={el => { cardsRef.current[i] = el; }}
              className="bg-white text-black rounded-2xl shadow-lg flex-1 min-w-[180px] max-w-[220px] h-[180px] flex flex-col items-start justify-start cursor-pointer relative"
              style={{
                perspective: '800px',
                backfaceVisibility: 'hidden',
                fontWeight: 600,
                fontSize: 18,
                boxShadow: '0 8px 32px rgba(0,0,0,0.10)',
                transition: 'box-shadow 0.3s cubic-bezier(0.4,0,0.2,1)',
                padding: '24px 20px 16px 20px',
              }}
            >
              <span style={{ fontSize: 15, color: ORANGE, fontWeight: 700, position: 'absolute', top: 18, left: 20, letterSpacing: 1.2 }}>
                {i === 0 ? 'Strategy' : i === 1 ? 'Design' : 'Development'}
              </span>
              <span style={{ fontSize: 13, color: '#222', textAlign: 'left', marginTop: 48, fontWeight: 400, display: 'block', lineHeight: 1.4 }}>
                {i === 0 && 'Clear concepts and digital roadmaps for your business.'}
                {i === 1 && 'Visual storytelling and UI/UX that feels right.'}
                {i === 2 && 'Robust code and scalable solutions for real products.'}
              </span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

