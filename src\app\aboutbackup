import React from "react";

const ORANGE = "#e8561c";

const careerTimeline = [
  {
    period: "2010–2014",
    title: "Software Developer – Apprenticeship & company experience",
    desc: "Learned and worked in web development, UI/UX, and digital tools",
  },
  {
    period: "2014–2022",
    title: "Technical Specialist – German Armed Forces",
    desc: "Worked on systems, structure, and operations in high-responsibility settings",
  },
  {
    period: "2022–2024",
    title: "IT Leadership & Systems – Academic studies",
    desc: "Focused on strategy, infrastructure, and organizational thinking",
  },
  {
    period: "2025–Present",
    title: "Independent Developer",
    desc: "Building digital experiences at the intersection of code and design",
  },
];

const About = () => {
  return (
    <section
      style={{
        background: "#f0f0f0",
        width: "100%",
        minHeight: "100vh",
        fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        position: 'relative',
      }}
    >
      {/* Top label - ABOUT at very left of section */}
      <div style={{
        position: 'absolute',
        top: 120,
        left: 40,
        color: '#000',
        fontSize: 28,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (ABOUT)
      </div>
      {/* Right label - 01 at very right of section */}
      <div style={{
        position: 'absolute',
        top: '600px', // moved further down
        right: 40,
        color: ORANGE,
        fontSize: 54,
        fontWeight: 700,
        letterSpacing: 1.5,
        zIndex: 10,
      }}>
        (01)
      </div>
      <div style={{
        maxWidth: 1200,
        margin: '0 auto',
        padding: 0,
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        minHeight: '100vh',
        marginTop: 80,
        position: 'relative',
      }}>
        {/* Main content with left padding reduced */}
        <div style={{ padding: '0 40px 0 20px' }}>
          {/* Intro - much larger */}
          <div style={{
            color: '#000',
            fontSize: 64,
            fontWeight: 700,
            margin: '180px 0 80px 0',
            maxWidth: 900,
            alignSelf: 'flex-start',
            lineHeight: 1.15,
            marginLeft: -150,
          }}>
            I'm Marvin, a developer based in Germany.<br />
            <span style={{
              fontWeight: 400,
              fontSize: 40,
              display: 'block',
              marginTop: 32,
              lineHeight: 1.2,
            }}>
              I like solving problems with code, thinking things through properly, and making sure the result feels right – not just technically, but for people too.
            </span>
          </div>
          {/* Timeline (years/text) bleibt wie vorher, aber ohne Bild und mit mehr Abstand nach oben */}
          <div
            style={{
              width: '100%',
              marginTop: 120,
              minHeight: '60vh',
            }}
          >
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                gap: 0,
                justifyContent: 'flex-start',
              }}
            >
              {/* Card 1 */}
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    width: '100vw',
                    borderTop: '1px solid #ccc',
                    margin: 0,
                    position: 'relative',
                    left: '50%',
                    transform: 'translateX(-50%)',
                    marginTop: 64,
                  }}
                />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: 200,
                    width: '100%',
                    padding: '64px 0',
                    background: '#f0f0f0',
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      color: ORANGE,
                      fontWeight: 700,
                      fontSize: 20,
                      minWidth: 120,
                      textAlign: 'left',
                      lineHeight: 1.1,
                    }}
                  >
                    (2010–2014)
                  </div>
                  <div style={{ flex: 1, position: 'relative' }}>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 700,
                        fontSize: 44,
                        marginBottom: 24,
                        lineHeight: 1.15,
                        background: '#f0f0f0',
                        position: 'relative',
                        zIndex: 2,
                      }}
                    >
                      Software Developer – Apprenticeship & company experience
                    </div>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 400,
                        fontSize: 28,
                        lineHeight: 1.5,
                        position: 'relative',
                        zIndex: 1,
                        marginTop: -8,
                      }}
                    >
                      Learned and worked in web development, UI/UX, and digital tools
                    </div>
                  </div>
                </div>
              </div>
              {/* Card 2 */}
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    width: '100vw',
                    borderTop: '1px solid #ccc',
                    margin: 0,
                    position: 'relative',
                    left: '50%',
                    transform: 'translateX(-50%)',
                  }}
                />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: 200,
                    width: '100%',
                    padding: '64px 0',
                    background: '#f0f0f0',
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      color: ORANGE,
                      fontWeight: 700,
                      fontSize: 20,
                      minWidth: 120,
                      textAlign: 'left',
                      lineHeight: 1.1,
                    }}
                  >
                    (2014–2022)
                  </div>
                  <div style={{ flex: 1, position: 'relative' }}>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 700,
                        fontSize: 44,
                        marginBottom: 24,
                        lineHeight: 1.15,
                        background: '#f0f0f0',
                        position: 'relative',
                        zIndex: 2,
                      }}
                    >
                      Technical Specialist – German Armed Forces
                    </div>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 400,
                        fontSize: 28,
                        lineHeight: 1.5,
                        position: 'relative',
                        zIndex: 1,
                        marginTop: -8,
                      }}
                    >
                      Worked on systems, structure, and operations in high-responsibility settings
                    </div>
                  </div>
                </div>
              </div>
              {/* Card 3 */}
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    width: '100vw',
                    borderTop: '1px solid #ccc',
                    margin: 0,
                    position: 'relative',
                    left: '50%',
                    transform: 'translateX(-50%)',
                  }}
                />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: 200,
                    width: '100%',
                    padding: '64px 0',
                    background: '#f0f0f0',
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      color: ORANGE,
                      fontWeight: 700,
                      fontSize: 20,
                      minWidth: 120,
                      textAlign: 'left',
                      lineHeight: 1.1,
                    }}
                  >
                    (2022–2024)
                  </div>
                  <div style={{ flex: 1, position: 'relative' }}>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 700,
                        fontSize: 44,
                        marginBottom: 24,
                        lineHeight: 1.15,
                        background: '#f0f0f0',
                        position: 'relative',
                        zIndex: 2,
                      }}
                    >
                      IT Leadership & Systems – Academic studies
                    </div>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 400,
                        fontSize: 28,
                        lineHeight: 1.5,
                        position: 'relative',
                        zIndex: 1,
                        marginTop: -8,
                      }}
                    >
                      Focused on strategy, infrastructure, and organizational thinking
                    </div>
                  </div>
                </div>
              </div>
              {/* Card 4 */}
              <div style={{ width: '100%' }}>
                <div
                  style={{
                    width: '100vw',
                    borderTop: '1px solid #ccc',
                    margin: 0,
                    position: 'relative',
                    left: '50%',
                    transform: 'translateX(-50%)',
                  }}
                />
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'row',
                    alignItems: 'flex-start',
                    gap: 200,
                    width: '100%',
                    padding: '64px 0',
                    background: '#f0f0f0',
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      color: ORANGE,
                      fontWeight: 700,
                      fontSize: 20,
                      minWidth: 120,
                      textAlign: 'left',
                      lineHeight: 1.1,
                    }}
                  >
                    (2025–Present)
                  </div>
                  <div style={{ flex: 1, position: 'relative' }}>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 700,
                        fontSize: 44,
                        marginBottom: 24,
                        lineHeight: 1.15,
                        background: '#f0f0f0',
                        position: 'relative',
                        zIndex: 2,
                      }}
                    >
                      Independent Developer
                    </div>
                    <div
                      style={{
                        color: '#000',
                        fontWeight: 400,
                        fontSize: 28,
                        lineHeight: 1.5,
                        position: 'relative',
                        zIndex: 1,
                        marginTop: -8,
                      }}
                    >
                      Building digital experiences at the intersection of code and design
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* More About Me Button */}
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            marginTop: 80,
          }}>
            <button
              style={{
                background: 'transparent',
                color: ORANGE,
                border: `1px solid ${ORANGE}`,
                padding: '12px 24px',
                fontSize: 14,
                fontWeight: 600,
                borderRadius: 25,
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                fontFamily: 'Satoshi, Arial, Helvetica, sans-serif',
                letterSpacing: 0.5,
                textTransform: 'uppercase',
              }}
              onMouseEnter={(e) => {
                (e.target as HTMLButtonElement).style.background = ORANGE;
                (e.target as HTMLButtonElement).style.color = '#fff';
                (e.target as HTMLButtonElement).style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                (e.target as HTMLButtonElement).style.background = 'transparent';
                (e.target as HTMLButtonElement).style.color = ORANGE;
                (e.target as HTMLButtonElement).style.transform = 'translateY(0)';
              }}
            >
              more about me
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
