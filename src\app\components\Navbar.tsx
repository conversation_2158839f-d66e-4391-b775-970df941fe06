'use client';
import React, { useState } from "react";

const menuItems = ['ABOUT', 'WORK', 'SERVICES', 'CONTACT'];

const Navbar = () => {
  const [hovered, setHovered] = useState<string | null>(null);

  return (
    <nav
      style={{
        position: 'fixed',
        top: 30,
        left: '50%',
        transform: 'translateX(-50%) scale(1)',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(20px)',
        borderRadius: 50,
        padding: '10px 25px',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 100,
        boxShadow: '0 4px 20px rgba(0,0,0,0.06)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        minWidth: 0,
        maxWidth: '90vw',
        fontFamily: 'Satoshi, Inter, Segoe UI, Arial, sans-serif',
        transition: 'all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1)',
        width: 'auto',
        height: 'auto',
      }}
    >
      <div style={{
        display: 'flex',
        gap: 8,
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        opacity: 1,
        transform: 'scale(1)',
        transition: 'all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1)',
        pointerEvents: 'auto',
      }}>
        {menuItems.map((item) => (
          <a
            key={item}
            href={`#${item.toLowerCase()}`}
            style={{
              color: '#1a1a1a',
              textDecoration: 'none',
              fontWeight: 500,    
              fontSize: 13,
              letterSpacing: 0.3,
              padding: '8px 16px',
              borderRadius: 20,
              background: hovered === item ? '#FF6B35' : 'transparent',
              width: 75,
              height: 36,
              textAlign: 'center',
              transition: 'all 0.2s ease',
              position: 'relative',
              cursor: 'pointer',
              userSelect: 'none',
              lineHeight: 1.2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transform: hovered === item ? 'translateY(-1px)' : 'translateY(0)',
              boxShadow: hovered === item 
                ? '0 4px 12px rgba(255, 107, 53, 0.2)' 
                : 'none',
            }}
            onMouseOver={() => setHovered(item)}
            onMouseOut={() => setHovered(null)}
          >
            <span style={{
              color: hovered === item ? '#ffffff' : '#1a1a1a',
              display: 'inline-block',
              textAlign: 'center',
              transition: 'all 0.3s ease',
              fontFamily: 'inherit',
              fontWeight: hovered === item ? 600 : 500,
              letterSpacing: hovered === item ? 0.5 : 0.3,
              whiteSpace: 'nowrap',
              fontSize: hovered === item ? 14 : 13,
              transform: hovered === item ? 'scale(1.05)' : 'scale(1)',
            }}>
              {item}
            </span>
          </a>
        ))}
      </div>
    </nav>
  );
};

export default Navbar;
